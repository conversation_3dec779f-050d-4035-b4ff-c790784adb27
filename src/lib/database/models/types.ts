import { Document } from 'mongoose';

// Enums
export enum AccountType {
  OAUTH = 'oauth',
}

export enum VCSProviderType {
  GITHUB = 'github',
  GITLAB = 'gitlab',
  BITBUCKET = 'bitbucket',
}

export enum PullRequestStatus {
  OPEN = 'open',
  CLOSED = 'closed',
  MERGED = 'merged',
  DRAFT = 'draft',
}

export enum ReviewStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
}

// Interface definitions
export interface IAccount extends Document {
  _id: string;
  userId: string;
  type: AccountType;
  provider: string;
  providerAccountId: string;
  refresh_token?: string;
  access_token?: string;
  expires_at?: number;
  token_type?: string;
  scope?: string;
  id_token?: string;
  session_state?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface ISession extends Document {
  _id: string;
  sessionToken: string;
  userId: string;
  expires: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface IUser extends Document {
  _id: string;
  name?: string;
  email?: string;
  emailVerified?: Date;
  image?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface IVerificationToken extends Document {
  _id: string;
  identifier: string;
  token: string;
  expires: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface IVCSProvider extends Document {
  _id: string;
  type: VCSProviderType;
  name: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface IVCSInstallation extends Document {
  _id: string;
  userId: string;
  providerId: string;
  installationId: string;
  accountId: string;
  accountLogin: string;
  accessToken?: string;
  tokenExpiresAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface IRepository extends Document {
  _id: string;
  vcsInstallationId: string;
  externalId: string;
  name: string;
  fullName: string;
  private: boolean;
  defaultBranch: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface IPullRequest extends Document {
  _id: string;
  repositoryId: string;
  externalId: string;
  number: number;
  title: string;
  description?: string;
  sourceBranch: string;
  targetBranch: string;
  status: PullRequestStatus;
  author: string;
  authorId: string;
  url: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface IReview extends Document {
  _id: string;
  pullRequestId: string;
  externalId?: string;
  status: ReviewStatus;
  summary?: string;
  processingTime?: number;
  createdAt: Date;
  updatedAt: Date;
}
